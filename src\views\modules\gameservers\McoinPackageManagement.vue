<template>
  <div class="mcoin-package-management-wrapper">
    <PageBreadcrumb 
      :page-title="currentPageTitle" 
      :breadcrumbs="[{ label: 'Quản lý Game Server', to: '/gameservers' }]" 
    />

    <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6 dark:border-gray-800 dark:bg-white/[0.03]">
      <!-- Type Tabs -->
      <div class="mb-6">
        <el-tabs 
          v-model="selectedType" 
          type="card"
          class="package-type-tabs"
          @tab-change="handleTypeChange"
        >
          <el-tab-pane
            v-for="option in typeOptions"
            :key="option.value"
            :label="option.label"
            :name="option.value"
          />
        </el-tabs>
      </div>

      <!-- Filters -->
      <div class="mb-6 rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-900/50">
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <el-input
              v-model="searchTerm"
              :placeholder="`<PERSON><PERSON><PERSON> kiếm theo tên ${typeLabels[selectedType]}...`"
              :prefix-icon="SearchIcon"
              @clear="handleResetFilters"
              clearable
              style="width: 300px"
              size="large"
            />
          </div>
          <div class="flex items-center gap-2">
            <el-select
              v-model="selectedStatus"
              placeholder="Lọc theo trạng thái"
              @change="handleStatusFilter"
              clearable
              style="width: 200px"
              size="large"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          <div class="ml-auto flex items-center gap-3">
            <ButtonCommon type="primary" @click="openAddModal" :icon="PlusIcon">
              {{ `Thêm ${typeLabels[selectedType]}` }}
            </ButtonCommon>
          </div>
        </div>
      </div>

      <!-- Packages Table -->
      <div
        class="table-container overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800"
        style="overflow-x: auto;"
      >
        <el-table
          v-loading="loading"
          :data="paginatedPackages"
          row-key="id"
          style="width: 100%"
          :header-cell-style="getTableHeaderStyle"
          :cell-style="getTableCellStyle"
          table-layout="auto"
          class="mcoin-package-table"
          :row-class-name="getRowClassName"
          :empty-text="`Chưa có ${typeLabels[selectedType]} nào`"
        >
          <!-- STT Column -->
          <el-table-column label="STT" width="80" align="center">
            <template #default="{ $index }">
              <span class="text-sm text-gray-600">{{ (currentPage - 1) * pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>

          <!-- Name Column -->
          <el-table-column label="Tên gói" min-width="200" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2 p-2">
                <img v-if="row?.icon" :src="row.icon" alt="Icon" class="w-8 h-8 rounded" />
                <span class="font-medium text-gray-900 dark:text-white">{{ row?.name }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- Required Value Column -->
          <el-table-column label="Giá trị yêu cầu" min-width="150" align="center">
            <template #default="{ row }">
              <div class="text-center">
                <span class="font-medium text-blue-600">{{ row?.required_value?.toLocaleString() }}</span>
                <div class="text-xs text-gray-500">{{ row?.type === 1 ? 'VNĐ' : 'Mcoin' }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- Received Value Column -->
          <el-table-column label="Giá trị nhận được" min-width="150" align="center">
            <template #default="{ row }">
              <div class="text-center">
                <span class="font-medium text-green-600">{{ row?.received_value?.toLocaleString() }}</span>
                <div class="text-xs text-gray-500">{{ row?.type === 1 ? 'Mcoin' : 'Xu' }}</div>
              </div>
            </template>
          </el-table-column>

          <!-- Status Column -->
          <el-table-column label="Trạng thái" width="150" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row?.status === 1 ? 'success' : 'danger'"
                size="large"
              >
                {{ row?.status_label }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- Created Date Column -->
          <el-table-column label="Ngày tạo" width="150" align="center">
            <template #default="{ row }">
              <div class="text-center text-sm text-gray-600 dark:text-gray-300">
                {{ formatDate(row?.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- Actions Column -->
          <el-table-column label="HÀNH ĐỘNG" min-width="100" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center gap-2">
                <ActionButtons
                  :show-view="false"
                  :show-edit="true"
                  :show-delete="true"
                  @edit="openEditModal(row)"
                  @delete="confirmDelete(row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- Pagination -->
      <Pagination :pagination="pagination" @page-change="handlePageChange" @per-page-change="handlePerPageChange" />
    </div>

    <!-- Package Modal -->
    <McoinPackageModal
      ref="packageModalRef"
      v-model="showModal"
      :editing-package="editingPackage"
      :package-type="selectedType"
      @close="closeModal"
      @save="handleSavePackage"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage, ElTabs, ElTabPane } from 'element-plus'
import PageBreadcrumb from '@/components/common/PageBreadcrumb.vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
import Pagination from '@/components/common/Pagination.vue'
import ActionButtons from '@/components/common/ActionButtons.vue'
import McoinPackageModal from '@/components/modules/gameservers/McoinPackageModal.vue'
import { PlusIcon } from '@/components/icons/index.js'
import { gamePackagesApi } from '@/utils/apis/index.js'

// Icons
const SearchIcon = Search

// Type definitions
const typeOptions = [
  { label: 'Nạp Mcoin', value: 1 },
  { label: 'Đổi Mcoin sang xu', value: 2 }
]

const typeLabels = {
  1: 'Nạp Mcoin',
  2: 'Đổi Mcoin sang xu'
}

// State
const selectedType = ref(1)
const loading = ref(false)
const mcoinPackages = ref([])
const paginationData = ref({})
const showModal = ref(false)
const editingPackage = ref(null)
const packageModalRef = ref(null)

// Filter states
const searchTerm = ref('')
const selectedStatus = ref('')

// Pagination
const currentPage = ref(1)
const pageSize = ref(15)

// Computed
const currentPageTitle = computed(() => {
  return selectedType.value === 1
    ? 'Quản lý Nạp Mcoin'
    : 'Quản lý Đổi Mcoin sang xu'
})

const paginatedPackages = computed(() => {
  return mcoinPackages.value || []
})

const pagination = computed(() => ({
  current_page: currentPage.value,
  per_page: pageSize.value,
  total: paginationData.value?.total || 0,
  from: paginationData.value?.from || 0,
  to: paginationData.value?.to || 0,
  last_page: paginationData.value?.last_page || 1,
  has_more_pages: paginationData.value?.has_more_pages || false,
}))

// Status options
const statusOptions = [
  { label: 'Đang hoạt động', value: 1 },
  { label: 'Không hoạt động', value: 0 }
]



// Table styling
const getTableHeaderStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color-page)',
    color: 'var(--el-text-color-primary)',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    fontSize: '14px',
    padding: '16px 12px',
    textAlign: 'center',
    height: '48px',
  }
}

const getTableCellStyle = () => {
  return {
    backgroundColor: 'var(--el-bg-color)',
    color: 'var(--el-text-color-primary)',
    borderColor: 'var(--el-border-color-lighter)',
    padding: '16px 12px',
    fontSize: '14px',
  }
}

const getRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// Methods
const loadPackages = async () => {
  loading.value = true
  try {
    const params = {
      type: selectedType.value,
      per_page: pageSize.value,
      page: currentPage.value,
    }

    // Add search term if exists
    if (searchTerm.value.trim()) {
      params.name = searchTerm.value.trim()
    }

    // Add status filter if exists
    if (selectedStatus.value !== '') {
      params.status = selectedStatus.value
    }

    const response = await gamePackagesApi.getGamePackages(params)

    if (response?.data?.success) {
      mcoinPackages.value = response.data.data?.data || []
      paginationData.value = response.data.data?.pagination || {}
    } else {
      ElMessage.error(response?.data?.message || 'Có lỗi xảy ra khi tải dữ liệu')
      mcoinPackages.value = []
      paginationData.value = {}
    }
  } catch (error) {
    console.error('Error loading packages:', error)
    ElMessage.error('Có lỗi xảy ra khi tải dữ liệu')
    mcoinPackages.value = []
    paginationData.value = {}
  } finally {
    loading.value = false
  }
}

const handleTypeChange = () => {
  // Reset filters when changing type
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
  // Load packages for new type
  loadPackages()
}

const handleStatusFilter = async () => {
  currentPage.value = 1 // Reset to first page when filtering
  loadPackages()
}

const handleResetFilters = async () => {
  searchTerm.value = ''
  selectedStatus.value = ''
  currentPage.value = 1
  loadPackages()
}

const handlePageChange = (page) => {
  currentPage.value = page
  loadPackages()
}

const handlePerPageChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadPackages()
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN')
  } catch (error) {
    return '-'
  }
}



const openAddModal = () => {
  editingPackage.value = null
  showModal.value = true
}

const openEditModal = (pkg) => {
  editingPackage.value = pkg
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  editingPackage.value = null
}

const handleSavePackage = async (packageData) => {
  try {
    // Prepare FormData for file upload
    const formData = new FormData()

    // Add all fields to FormData
    Object.entries(packageData).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formData.append(key, value)
      }
    })

    let response
    if (editingPackage.value) {
      // Update existing package
      response = await gamePackagesApi.updateGamePackage(editingPackage.value.id, formData)
    } else {
      // Add new package
      response = await gamePackagesApi.createGamePackage(formData)
    }

    // Handle response
    if (response?.data?.success) {
      const action = editingPackage.value ? 'Cập nhật' : 'Thêm'
      ElMessage.success(`${action} ${typeLabels[selectedType.value]} thành công!`)
      loadPackages() // Reload data
      closeModal()
    } else {
      // Handle validation errors
      if (response?.data?.errors) {
        // Pass errors to modal for field-specific display
        if (packageModalRef.value?.handleServerValidationErrors) {
          packageModalRef.value.handleServerValidationErrors(response.data.errors)
        }
        const firstErrorKey = Object.keys(response.data.errors)[0]
        const firstErrorMessage = response.data.errors[firstErrorKey]?.[0]
        ElMessage.error(firstErrorMessage || response?.data?.message || 'Dữ liệu không hợp lệ')
      } else {
        ElMessage.error(response?.data?.message || 'Có lỗi xảy ra khi lưu dữ liệu')
      }
      // Reset saving state in modal
      if (packageModalRef.value) {
        packageModalRef.value.saving = false
      }
    }
  } catch (error) {
    console.error('Error saving package:', error)

    // Handle validation errors (422 status from axios interceptor)
    if (error?.errors) {
      // Pass errors to modal for field-specific display
      if (packageModalRef.value?.handleServerValidationErrors) {
        packageModalRef.value.handleServerValidationErrors(error.errors)
      }
      const firstErrorKey = Object.keys(error.errors)[0]
      const firstErrorMessage = error.errors[firstErrorKey]?.[0]
      ElMessage.error(firstErrorMessage || error?.message || 'Dữ liệu không hợp lệ')
    } else if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('Có lỗi xảy ra khi lưu dữ liệu')
    }

    // Reset saving state in modal
    if (packageModalRef.value) {
      packageModalRef.value.saving = false
    }
  }
}

const confirmDelete = async (pkg) => {
  try {
    await ElMessageBox.confirm(
      `Bạn có chắc chắn muốn xóa ${pkg?.type_label || typeLabels[pkg?.type]} "${pkg?.name}" không?`,
      'Xác nhận xóa',
      {
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy',
        type: 'warning',
      }
    )

    const response = await gamePackagesApi.deleteGamePackage(pkg.id)
    if (response?.data?.success) {
      ElMessage.success(`Xóa ${pkg?.type_label || typeLabels[pkg?.type]} thành công!`)
      loadPackages() // Reload data
    } else {
      ElMessage.error(response?.data?.message || 'Có lỗi xảy ra khi xóa')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Error deleting package:', error)
      ElMessage.error('Có lỗi xảy ra khi xóa')
    }
  }
}

// Auto search when input changes
let searchTimeout = null
watch(searchTerm, () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    currentPage.value = 1 // Reset to first page when searching
    loadPackages()
  }, 300)
})

// Lifecycle
onMounted(() => {
  loadPackages()
})
</script>

<style lang="scss" scoped>
/* Enhanced table styling */
:deep(.mcoin-package-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.mcoin-package-table .even-row) {
  background-color: var(--el-bg-color);
}

:deep(.mcoin-package-table .odd-row) {
  background-color: var(--el-fill-color-light);
}

:deep(.mcoin-package-table .even-row:hover),
:deep(.mcoin-package-table .odd-row:hover) {
  background-color: var(--el-fill-color) !important;
}

:deep(.mcoin-package-table .el-table__header-wrapper) {
  background-color: var(--el-bg-color-page);
}

:deep(.mcoin-package-table .el-table__row) {
  transition: background-color 0.2s ease;
  height: 56px !important;
  min-height: 56px !important;
}

:deep(.mcoin-package-table .el-table__cell) {
  border-color: var(--el-border-color-lighter);
  vertical-align: middle;
}

/* Ensure all table headers have consistent styling */
:deep(.mcoin-package-table .el-table__header .el-table__cell) {
  background-color: var(--el-bg-color-page) !important;
  color: var(--el-text-color-primary) !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 14px !important;
  padding: 12px !important;
  border-bottom: 1px solid var(--el-border-color-light) !important;
  text-align: center !important;
  height: 48px !important;
}

/* Ensure all table body cells have consistent styling */
:deep(.mcoin-package-table .el-table__body .el-table__cell) {
  background-color: var(--el-bg-color) !important;
  color: var(--el-text-color-primary) !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  padding: 12px !important;
  font-size: 14px !important;
  height: 56px !important;
  vertical-align: middle !important;
}

/* Table empty state styling */
:deep(.mcoin-package-table .el-table__empty-block) {
  background-color: var(--el-bg-color);
  border: none;
  padding: 40px 20px;
}

:deep(.mcoin-package-table .el-table__empty-text) {
  color: var(--el-text-color-regular);
  font-size: 14px;
  font-weight: 500;
}

/* Dark mode support */
.dark :deep(.mcoin-package-table .el-table__empty-block) {
  background-color: #1e293b;
}

.dark :deep(.mcoin-package-table .el-table__empty-text) {
  color: #94a3b8;
}

/* Switch styling */
:deep(.mcoin-package-table .el-switch) {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* Segmented control styling */
:deep(.el-segmented) {
  --el-segmented-bg-color: var(--el-bg-color);
  --el-segmented-item-selected-bg-color: var(--el-color-primary);
  --el-segmented-item-selected-color: #ffffff;
}
</style>
